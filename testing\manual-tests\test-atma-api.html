<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ATMA API - PetaTalenta</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #1e293b;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            transition: background-color 0.2s;
        }
        .button:hover {
            background: #2563eb;
        }
        .button.danger {
            background: #ef4444;
        }
        .button.danger:hover {
            background: #dc2626;
        }
        .response {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #dcfce7;
            border-color: #bbf7d0;
            color: #166534;
        }
        .error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #991b1b;
        }
        .info {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 ATMA API Test Tool</h1>
        
        <div class="info">
            <strong>API Base URL:</strong> https://api.chhrone.web.id<br>
            <strong>Purpose:</strong> Test authentication endpoints before integrating with the main application.
        </div>

        <h2>🔐 Test Registration</h2>
        <div class="form-group">
            <label for="regEmail">Email:</label>
            <input type="email" id="regEmail" placeholder="<EMAIL>" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="regPassword">Password:</label>
            <input type="password" id="regPassword" placeholder="password123" value="password123">
        </div>
        <div class="form-group">
            <label for="regName">Name (optional):</label>
            <input type="text" id="regName" placeholder="Test User" value="Test User">
        </div>
        <button class="button" onclick="testRegister()">📝 Test Register</button>
        
        <div id="registerResponse"></div>

        <h2>🔑 Test Login</h2>
        <div class="form-group">
            <label for="loginEmail">Email:</label>
            <input type="email" id="loginEmail" placeholder="<EMAIL>" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="loginPassword">Password:</label>
            <input type="password" id="loginPassword" placeholder="password123" value="password123">
        </div>
        <button class="button" onclick="testLogin()">🔓 Test Login</button>
        
        <div id="loginResponse"></div>

        <h2>🎯 Test with Saved Token</h2>
        <div class="form-group">
            <label for="savedToken">Saved Token:</label>
            <input type="text" id="savedToken" placeholder="Token will appear here after successful login" readonly>
        </div>
        <button class="button" onclick="testTokenValidation()">✅ Validate Token</button>
        <button class="button" onclick="testGetProfile()">👤 Get Profile</button>
        
        <div id="tokenResponse"></div>

        <h2>🧹 Clear Data</h2>
        <button class="button danger" onclick="clearAllData()">🗑️ Clear All Test Data</button>
    </div>

    <script>
        const API_BASE_URL = 'https://api.chhrone.web.id';
        let currentToken = null;

        function showResponse(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `response ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function testRegister() {
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const name = document.getElementById('regName').value;

            try {
                console.log('Testing registration...');
                const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        name: name || undefined
                    }),
                });

                const data = await response.json();
                console.log('Register response:', data);

                if (data.success && data.data?.token) {
                    currentToken = data.data.token;
                    document.getElementById('savedToken').value = currentToken;
                    showResponse('registerResponse', {
                        status: response.status,
                        success: true,
                        message: 'Registration successful!',
                        data: data
                    });
                } else {
                    showResponse('registerResponse', {
                        status: response.status,
                        success: false,
                        error: data.error || 'Registration failed',
                        fullResponse: data
                    }, true);
                }
            } catch (error) {
                console.error('Register error:', error);
                showResponse('registerResponse', {
                    error: 'Network error',
                    message: error.message,
                    details: error
                }, true);
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                console.log('Testing login...');
                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    }),
                });

                const data = await response.json();
                console.log('Login response:', data);

                if (data.success && data.data?.token) {
                    currentToken = data.data.token;
                    document.getElementById('savedToken').value = currentToken;
                    showResponse('loginResponse', {
                        status: response.status,
                        success: true,
                        message: 'Login successful!',
                        data: data
                    });
                } else {
                    showResponse('loginResponse', {
                        status: response.status,
                        success: false,
                        error: data.error || 'Login failed',
                        fullResponse: data
                    }, true);
                }
            } catch (error) {
                console.error('Login error:', error);
                showResponse('loginResponse', {
                    error: 'Network error',
                    message: error.message,
                    details: error
                }, true);
            }
        }

        async function testTokenValidation() {
            const token = document.getElementById('savedToken').value || currentToken;
            
            if (!token) {
                showResponse('tokenResponse', {
                    error: 'No token available',
                    message: 'Please login first to get a token'
                }, true);
                return;
            }

            try {
                console.log('Testing token validation...');
                const response = await fetch(`${API_BASE_URL}/api/auth/validate`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('Token validation response:', data);

                showResponse('tokenResponse', {
                    status: response.status,
                    valid: response.ok,
                    data: data
                }, !response.ok);
            } catch (error) {
                console.error('Token validation error:', error);
                showResponse('tokenResponse', {
                    error: 'Network error',
                    message: error.message,
                    details: error
                }, true);
            }
        }

        async function testGetProfile() {
            const token = document.getElementById('savedToken').value || currentToken;
            
            if (!token) {
                showResponse('tokenResponse', {
                    error: 'No token available',
                    message: 'Please login first to get a token'
                }, true);
                return;
            }

            try {
                console.log('Testing get profile...');
                const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('Get profile response:', data);

                showResponse('tokenResponse', {
                    status: response.status,
                    success: response.ok,
                    data: data
                }, !response.ok);
            } catch (error) {
                console.error('Get profile error:', error);
                showResponse('tokenResponse', {
                    error: 'Network error',
                    message: error.message,
                    details: error
                }, true);
            }
        }

        function clearAllData() {
            currentToken = null;
            document.getElementById('savedToken').value = '';
            document.getElementById('registerResponse').innerHTML = '';
            document.getElementById('loginResponse').innerHTML = '';
            document.getElementById('tokenResponse').innerHTML = '';
            
            // Reset form values
            document.getElementById('regEmail').value = '<EMAIL>';
            document.getElementById('regPassword').value = 'password123';
            document.getElementById('regName').value = 'Test User';
            document.getElementById('loginEmail').value = '<EMAIL>';
            document.getElementById('loginPassword').value = 'password123';
            
            console.log('All test data cleared');
        }
    </script>
</body>
</html>
