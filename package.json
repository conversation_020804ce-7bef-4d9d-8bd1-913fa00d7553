{"name": "petatalenta-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "build:analyze": "set ANALYZE=true && next build", "dev": "next dev", "lint": "next lint", "start": "next start", "start:websocket": "node testing/mock-servers/mock-websocket-server.js", "dev:full": "concurrently \"npm run dev\" \"npm run start:websocket\"", "test:websocket": "node testing/scripts/test-websocket.js", "test:websocket:api": "node testing/scripts/test-websocket-api.js", "test:token:quick": "node testing/scripts/quick-token-test.js", "test:token:concurrent": "node testing/scripts/concurrent-assessment-test.js", "test:setup-users": "node testing/scripts/setup-test-users.js", "test:high-load": "node testing/scripts/run-concurrent-tests.js", "test:summary": "node testing/scripts/test-summary.js"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/html2canvas": "^0.5.35", "@types/jspdf": "^1.3.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "comlink": "^4.4.2", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "geist": "^1.3.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "input-otp": "1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "react": "^18.3.1", "react-day-picker": "^9.0.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.4", "swr": "^2.3.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "web-vitals": "^5.1.0", "zod": "^3.24.1"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.5", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}