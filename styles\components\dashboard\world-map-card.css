/* World Map Card Component Styles */
.world-map-card {
  @apply bg-white;
  border-color: #eaecf0;
}

.world-map-card__content {
  @apply flex flex-col space-y-1.5 p-6;
}

.world-map-card__viais-header {
  @apply text-center mb-6;
}

.world-map-card__viais-title {
  @apply text-lg font-semibold mb-3;
  color: #1e1e1e;
}

.world-map-card__strengths-grid {
  @apply grid grid-cols-2 gap-2 mb-4;
}

.world-map-card__strength-item {
  @apply rounded-lg p-2;
  background-color: #f8f9fa;
}

.world-map-card__strength-name {
  @apply text-xs font-medium truncate;
  color: #64707d;
}

.world-map-card__strength-score {
  @apply text-sm font-semibold;
  color: #1e1e1e;
}

.world-map-card__viais-description {
  @apply text-xs;
  color: #64707d;
}

.world-map-card__ocean-container {
  @apply flex items-center justify-center gap-2;
}

.world-map-card__ocean-bar-item {
  @apply flex flex-col items-center gap-1 flex-1;
}

.world-map-card__ocean-percentage-container {
  @apply h-6 flex items-center justify-center;
}

.world-map-card__ocean-percentage {
  @apply text-xs font-semibold;
  color: #1e1e1e;
}

.world-map-card__ocean-bar-background {
  @apply relative w-full rounded-lg overflow-hidden;
  height: 128px;
  background-color: #f3f3f3;
}

.world-map-card__ocean-bar-fill {
  @apply absolute bottom-0 w-full transition-all duration-300;
  min-height: 20px;
  /* Height and background-color will be set dynamically */
}

.world-map-card__ocean-trait-label {
  @apply text-xs font-medium;
  color: #1e1e1e;
}
