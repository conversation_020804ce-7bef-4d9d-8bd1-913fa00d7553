<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Double Submission Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .step { color: #6f42c1; font-weight: bold; }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        .status.pending { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Double Submission Fix Test Suite</h1>
        <p>Test suite untuk memverifikasi bahwa masalah double submission telah diperbaiki.</p>
        
        <div class="test-section">
            <h3>📊 Current State</h3>
            <div id="currentState"></div>
            <button onclick="checkCurrentState()">Check Current State</button>
            <button onclick="clearAllData()">Clear All Test Data</button>
        </div>

        <div class="test-section">
            <h3>🛡️ Submission Guard Tests</h3>
            <div id="submissionGuardStatus"></div>
            <button onclick="testSubmissionGuard()">Test Submission Guard</button>
            <button onclick="testRapidSubmissions()">Test Rapid Submissions</button>
            <button onclick="testCooldownPeriod()">Test Cooldown Period</button>
        </div>

        <div class="test-section">
            <h3>📝 History Management Tests</h3>
            <div id="historyStatus"></div>
            <button onclick="testHistoryDuplication()">Test History Duplication</button>
            <button onclick="testHistoryDeduplication()">Test History Deduplication</button>
            <button onclick="testHistoryRetrieval()">Test History Retrieval</button>
        </div>

        <div class="test-section">
            <h3>🔄 Workflow Callback Tests</h3>
            <div id="callbackStatus"></div>
            <button onclick="testCallbackProtection()">Test Callback Protection</button>
            <button onclick="testWorkflowReset()">Test Workflow Reset</button>
        </div>

        <div class="test-section">
            <h3>🧪 Integration Tests</h3>
            <div id="integrationStatus"></div>
            <button onclick="testFullSubmissionFlow()">Test Full Submission Flow</button>
            <button onclick="testPartialSubmissionFlow()">Test Partial Submission Flow</button>
            <button onclick="testLoadingPageFlow()">Test Loading Page Flow</button>
        </div>

        <div class="test-section">
            <h3>📋 Test Results Summary</h3>
            <div id="testResults"></div>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="generateReport()">Generate Report</button>
        </div>

        <div class="test-section">
            <h3>📜 Test Log</h3>
            <div id="testLog" class="log"></div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        // Test utilities
        let testResults = {};
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${status}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }

        // Mock assessment answers for testing
        const mockAnswers = {
            1: 4, 2: 3, 3: 5, 4: 2, 5: 4,
            6: 3, 7: 5, 8: 2, 9: 4, 10: 3
        };

        // Test 1: Check current state
        function checkCurrentState() {
            log('🔍 Checking current application state...', 'step');
            
            const history = JSON.parse(localStorage.getItem('assessment-history') || '[]');
            const progress = localStorage.getItem('assessment-progress');
            const recentSubmissions = Object.keys(localStorage).filter(key => 
                key.startsWith('recent-submission-')
            );

            log(`📊 Assessment history: ${history.length} items`, 'info');
            log(`💾 Assessment progress: ${progress ? 'exists' : 'none'}`, 'info');
            log(`⏰ Recent submissions: ${recentSubmissions.length}`, 'info');

            const stateInfo = `
                <strong>Assessment History:</strong> ${history.length} items<br>
                <strong>Assessment Progress:</strong> ${progress ? 'Saved' : 'None'}<br>
                <strong>Recent Submissions:</strong> ${recentSubmissions.length}<br>
                <strong>LocalStorage Keys:</strong> ${Object.keys(localStorage).length}
            `;
            
            updateStatus('currentState', 'pass', stateInfo);
        }

        // Test 2: Submission Guard
        function testSubmissionGuard() {
            log('🛡️ Testing submission guard...', 'step');
            
            try {
                // Simulate submission guard check
                const submissionKey = btoa(JSON.stringify(mockAnswers)).slice(0, 16);
                const recentKey = `recent-submission-${submissionKey}`;
                
                // Test 1: No recent submission
                localStorage.removeItem(recentKey);
                const hasRecent1 = localStorage.getItem(recentKey) !== null;
                log(`✅ No recent submission check: ${!hasRecent1 ? 'PASS' : 'FAIL'}`, hasRecent1 ? 'error' : 'success');
                
                // Test 2: Mark recent submission
                localStorage.setItem(recentKey, Date.now().toString());
                const hasRecent2 = localStorage.getItem(recentKey) !== null;
                log(`✅ Mark recent submission: ${hasRecent2 ? 'PASS' : 'FAIL'}`, hasRecent2 ? 'success' : 'error');
                
                // Test 3: Detect recent submission
                const now = Date.now();
                const lastSubmission = parseInt(localStorage.getItem(recentKey) || '0', 10);
                const isRecent = (now - lastSubmission) < 30000; // 30 seconds
                log(`✅ Detect recent submission: ${isRecent ? 'PASS' : 'FAIL'}`, isRecent ? 'success' : 'error');
                
                testResults.submissionGuard = isRecent;
                updateStatus('submissionGuardStatus', isRecent ? 'pass' : 'fail', 
                    isRecent ? 'Submission guard working correctly' : 'Submission guard failed');
                
            } catch (error) {
                log(`❌ Submission guard test failed: ${error.message}`, 'error');
                testResults.submissionGuard = false;
                updateStatus('submissionGuardStatus', 'fail', `Error: ${error.message}`);
            }
        }

        // Test 3: Rapid submissions
        function testRapidSubmissions() {
            log('⚡ Testing rapid submissions...', 'step');
            
            const submissionKey = btoa(JSON.stringify(mockAnswers)).slice(0, 16);
            const recentKey = `recent-submission-${submissionKey}`;
            
            // Clear previous state
            localStorage.removeItem(recentKey);
            
            let submissionCount = 0;
            let blockedCount = 0;
            
            // Simulate 5 rapid submissions
            for (let i = 0; i < 5; i++) {
                const hasRecent = localStorage.getItem(recentKey) !== null;
                
                if (!hasRecent) {
                    // First submission - should be allowed
                    localStorage.setItem(recentKey, Date.now().toString());
                    submissionCount++;
                    log(`📤 Submission ${i + 1}: ALLOWED`, 'success');
                } else {
                    // Subsequent submissions - should be blocked
                    blockedCount++;
                    log(`🚫 Submission ${i + 1}: BLOCKED`, 'warning');
                }
            }
            
            const testPassed = submissionCount === 1 && blockedCount === 4;
            log(`📊 Results: ${submissionCount} allowed, ${blockedCount} blocked`, 'info');
            
            testResults.rapidSubmissions = testPassed;
            updateStatus('submissionGuardStatus', testPassed ? 'pass' : 'fail',
                testPassed ? 'Rapid submission protection working' : 'Rapid submission protection failed');
        }

        // Test 4: History duplication
        function testHistoryDuplication() {
            log('📝 Testing history duplication prevention...', 'step');
            
            const testResultId = 'test-result-' + Date.now();
            const testItem = {
                id: Date.now(),
                nama: "Test Assessment",
                tipe: "Personality Assessment",
                tanggal: new Date().toLocaleDateString('id-ID'),
                status: "Selesai",
                resultId: testResultId
            };
            
            // Clear existing history
            localStorage.setItem('assessment-history', '[]');
            
            // Add item first time
            let history = JSON.parse(localStorage.getItem('assessment-history') || '[]');
            history.unshift(testItem);
            localStorage.setItem('assessment-history', JSON.stringify(history));
            
            const countAfterFirst = history.length;
            log(`📊 After first addition: ${countAfterFirst} items`, 'info');
            
            // Try to add same item again (simulate duplicate)
            history = JSON.parse(localStorage.getItem('assessment-history') || '[]');
            const existingIndex = history.findIndex(item => item.resultId === testResultId);
            
            if (existingIndex === -1) {
                // No duplicate found, add new item
                history.unshift(testItem);
                localStorage.setItem('assessment-history', JSON.stringify(history));
            } else {
                log(`🔍 Duplicate detected, not adding`, 'warning');
            }
            
            const countAfterSecond = JSON.parse(localStorage.getItem('assessment-history') || '[]').length;
            log(`📊 After second addition: ${countAfterSecond} items`, 'info');
            
            const testPassed = countAfterFirst === countAfterSecond && countAfterFirst === 1;
            
            testResults.historyDuplication = testPassed;
            updateStatus('historyStatus', testPassed ? 'pass' : 'fail',
                testPassed ? 'History duplication prevention working' : 'History duplication prevention failed');
        }

        // Test 5: Full integration test
        function testFullSubmissionFlow() {
            log('🧪 Testing full submission flow...', 'step');
            
            // Clear state
            localStorage.removeItem('assessment-history');
            localStorage.removeItem('assessment-progress');
            
            // Simulate full submission flow
            const resultId = 'integration-test-' + Date.now();
            const submissionKey = btoa(JSON.stringify(mockAnswers)).slice(0, 16);
            const recentKey = `recent-submission-${submissionKey}`;
            
            let testsPassed = 0;
            const totalTests = 4;
            
            // Test 1: Check no recent submission
            localStorage.removeItem(recentKey);
            if (!localStorage.getItem(recentKey)) {
                testsPassed++;
                log('✅ No recent submission check: PASS', 'success');
            }
            
            // Test 2: Mark submission
            localStorage.setItem(recentKey, Date.now().toString());
            if (localStorage.getItem(recentKey)) {
                testsPassed++;
                log('✅ Mark submission: PASS', 'success');
            }
            
            // Test 3: Add to history
            const historyItem = {
                id: Date.now(),
                nama: "Integration Test Assessment",
                tipe: "Personality Assessment",
                tanggal: new Date().toLocaleDateString('id-ID'),
                status: "Selesai",
                resultId: resultId
            };
            
            const history = JSON.parse(localStorage.getItem('assessment-history') || '[]');
            history.unshift(historyItem);
            localStorage.setItem('assessment-history', JSON.stringify(history));
            
            if (JSON.parse(localStorage.getItem('assessment-history') || '[]').length === 1) {
                testsPassed++;
                log('✅ Add to history: PASS', 'success');
            }
            
            // Test 4: Prevent duplicate history
            const existingHistory = JSON.parse(localStorage.getItem('assessment-history') || '[]');
            const existingIndex = existingHistory.findIndex(item => item.resultId === resultId);
            
            if (existingIndex !== -1) {
                testsPassed++;
                log('✅ Prevent duplicate history: PASS', 'success');
            }
            
            const allTestsPassed = testsPassed === totalTests;
            
            testResults.fullSubmissionFlow = allTestsPassed;
            updateStatus('integrationStatus', allTestsPassed ? 'pass' : 'fail',
                `Integration test: ${testsPassed}/${totalTests} tests passed`);
        }

        // Clear all test data
        function clearAllData() {
            log('🧹 Clearing all test data...', 'step');
            
            const keysToRemove = Object.keys(localStorage).filter(key => 
                key.startsWith('assessment-') || 
                key.startsWith('recent-submission-') ||
                key.startsWith('test-')
            );
            
            keysToRemove.forEach(key => localStorage.removeItem(key));
            
            log(`🗑️ Removed ${keysToRemove.length} localStorage keys`, 'info');
            updateStatus('currentState', 'pass', 'All test data cleared');
        }

        // Run all tests
        function runAllTests() {
            log('🚀 Running all tests...', 'step');
            
            clearAllData();
            
            setTimeout(() => {
                testSubmissionGuard();
                setTimeout(() => {
                    testRapidSubmissions();
                    setTimeout(() => {
                        testHistoryDuplication();
                        setTimeout(() => {
                            testFullSubmissionFlow();
                            setTimeout(() => {
                                generateReport();
                            }, 500);
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }

        // Generate test report
        function generateReport() {
            log('📋 Generating test report...', 'step');
            
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            const failedTests = totalTests - passedTests;
            
            const reportHtml = `
                <h4>Test Summary</h4>
                <div class="status ${failedTests === 0 ? 'pass' : 'fail'}">
                    <strong>Total Tests:</strong> ${totalTests}<br>
                    <strong>Passed:</strong> ${passedTests}<br>
                    <strong>Failed:</strong> ${failedTests}<br>
                    <strong>Success Rate:</strong> ${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%
                </div>
                <h4>Individual Results:</h4>
                ${Object.entries(testResults).map(([test, result]) => 
                    `<div class="status ${result ? 'pass' : 'fail'}">${test}: ${result ? 'PASS' : 'FAIL'}</div>`
                ).join('')}
            `;
            
            updateStatus('testResults', failedTests === 0 ? 'pass' : 'fail', reportHtml);
            
            log(`📊 Test Report: ${passedTests}/${totalTests} tests passed`, 
                failedTests === 0 ? 'success' : 'error');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 Double Submission Fix Test Suite initialized', 'info');
            checkCurrentState();
        });

        // Placeholder functions for tests not yet implemented
        function testCooldownPeriod() {
            log('⏰ Cooldown period test not yet implemented', 'warning');
        }

        function testHistoryDeduplication() {
            log('🔄 History deduplication test not yet implemented', 'warning');
        }

        function testHistoryRetrieval() {
            log('📖 History retrieval test not yet implemented', 'warning');
        }

        function testCallbackProtection() {
            log('🔒 Callback protection test not yet implemented', 'warning');
        }

        function testWorkflowReset() {
            log('🔄 Workflow reset test not yet implemented', 'warning');
        }

        function testPartialSubmissionFlow() {
            log('📝 Partial submission flow test not yet implemented', 'warning');
        }

        function testLoadingPageFlow() {
            log('⏳ Loading page flow test not yet implemented', 'warning');
        }
    </script>
</body>
</html>
