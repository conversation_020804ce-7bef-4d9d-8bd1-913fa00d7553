# Screenshot Export Functionality

## Overview

Fitur screenshot export telah ditambahkan ke halaman hasil assessment untuk memungkinkan pengguna menyimpan tampilan halaman sebagai gambar PNG berkualitas tinggi.

## Features

### 1. Multiple Export Options
- **Screenshot Halaman Penuh**: Menangkap seluruh halaman termasuk area yang perlu di-scroll
- **Screenshot Konten Utama**: Menangkap hanya area konten utama (grid hasil assessment)
- **Screenshot Per Bagian**: Menangkap header dan konten sebagai file terpisah
- **Export Text (PDF)**: Opsi export text yang sudah ada sebelumnya

### 2. High Quality Output
- Resolusi tinggi dengan scale 1.5x untuk hasil yang tajam
- Format PNG dengan kualitas 95%
- Background putih konsisten
- Support untuk CORS dan handling gambar eksternal

### 3. User Experience
- Dropdown menu yang mudah digunakan
- Loading indicator saat proses screenshot
- Toast notifications untuk feedback
- Auto-scroll ke atas untuk capture yang optimal
- Filename dengan timestamp untuk menghindari duplikasi

## Technical Implementation

### Dependencies
```json
{
  "html2canvas": "^1.4.1",
  "@types/html2canvas": "^1.0.0"
}
```

### Core Files
- `utils/screenshot-utils.ts` - Utility functions untuk screenshot
- `app/results/[id]/page.tsx` - Main results page dengan export functionality

### Key Functions

#### `captureElementScreenshot(element, options)`
Menangkap screenshot dari element HTML tertentu.

#### `capturePageScreenshot(options)`
Menangkap screenshot dari seluruh halaman dengan handling scroll area.

#### `captureMultipleScreenshots(elements, options)`
Menangkap multiple elements sebagai screenshot terpisah.

#### `downloadBlob(blob, filename)`
Download blob sebagai file dengan nama yang ditentukan.

## Usage

### In Results Page
1. Buka halaman hasil assessment (`/results/[id]`)
2. Klik tombol "Export" di header
3. Pilih opsi screenshot yang diinginkan:
   - Screenshot Halaman Penuh
   - Screenshot Konten Utama  
   - Screenshot Per Bagian
   - Export Text (PDF)

### Programmatic Usage
```typescript
import { captureElementScreenshot, downloadBlob } from '../utils/screenshot-utils';

const handleScreenshot = async () => {
  const element = document.getElementById('target-element');
  if (!element) return;
  
  const screenshot = await captureElementScreenshot(element, {
    quality: 0.95,
    scale: 2,
    backgroundColor: '#ffffff'
  });
  
  downloadBlob(screenshot, 'my-screenshot.png');
};
```

## Configuration Options

### ScreenshotOptions
```typescript
interface ScreenshotOptions {
  quality?: number;        // 0-1, default: 1
  scale?: number;          // Scale factor, default: 2
  backgroundColor?: string; // Background color, default: '#ffffff'
  useCORS?: boolean;       // Enable CORS, default: true
  allowTaint?: boolean;    // Allow tainted canvas, default: false
  width?: number;          // Custom width
  height?: number;         // Custom height
}
```

## Error Handling

Fungsi screenshot dilengkapi dengan comprehensive error handling:
- Network errors untuk gambar eksternal
- Canvas tainted errors
- Element not found errors
- Memory limitations untuk halaman besar

## Browser Compatibility

- Chrome/Chromium: Full support
- Firefox: Full support
- Safari: Full support
- Edge: Full support
- Mobile browsers: Limited support (tergantung ukuran halaman)

## Performance Considerations

1. **Memory Usage**: Screenshot halaman besar dapat menggunakan memory signifikan
2. **Processing Time**: Halaman kompleks membutuhkan waktu lebih lama
3. **Image Quality vs File Size**: Scale dan quality yang tinggi menghasilkan file besar

## Testing

Test page tersedia di `/test-screenshot` untuk menguji functionality:
- Test screenshot individual elements
- Test screenshot konten area
- Test screenshot halaman penuh
- Test berbagai styling (gradient, transparansi, dll)

## Future Enhancements

1. **ZIP Export**: Multiple screenshots dalam satu ZIP file
2. **PDF Export**: Convert screenshots ke PDF
3. **Batch Processing**: Screenshot multiple pages sekaligus
4. **Custom Annotations**: Tambah watermark atau annotations
5. **Cloud Storage**: Upload langsung ke cloud storage

## Troubleshooting

### Common Issues

1. **Blank Screenshots**
   - Pastikan element visible saat capture
   - Check CORS settings untuk gambar eksternal
   - Verify element dimensions

2. **Poor Quality**
   - Increase scale factor
   - Adjust quality setting
   - Check viewport size

3. **Memory Errors**
   - Reduce scale factor
   - Capture smaller sections
   - Close other browser tabs

### Debug Mode
Enable console logging dengan menambahkan `logging: true` di html2canvas options.

## Support

Untuk issues atau feature requests terkait screenshot functionality, silakan buat issue di repository dengan label `screenshot` atau `export`.
