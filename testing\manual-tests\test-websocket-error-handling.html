<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Error Handling Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Error Handling Test</h1>
        <p>This page tests the WebSocket error handling fixes for the assessment service.</p>

        <div class="test-section info">
            <h3>Test Status</h3>
            <p id="status">Ready to test</p>
            <button onclick="runErrorHandlingTests()">Run Error Handling Tests</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>Console Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // Mock the WebSocket error handling functions
        function getErrorMessage(error) {
            if (!error) return 'Unknown error';
            
            // If it's already a string
            if (typeof error === 'string') return error;
            
            // If it has a message property
            if (error.message && typeof error.message === 'string') return error.message;
            
            // If it has a toString method
            if (typeof error.toString === 'function') {
                const stringified = error.toString();
                if (stringified !== '[object Object]') return stringified;
            }
            
            // If it's an object, try to stringify it
            if (typeof error === 'object') {
                try {
                    return JSON.stringify(error);
                } catch {
                    return 'Error object could not be serialized';
                }
            }
            
            return 'Unknown error type';
        }

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function addResult(testName, success, message) {
            const resultsElement = document.getElementById('results');
            const resultClass = success ? 'success' : 'error';
            const resultIcon = success ? '✅' : '❌';
            
            resultsElement.innerHTML += `
                <div class="test-section ${resultClass}">
                    <strong>${resultIcon} ${testName}</strong><br>
                    ${message}
                </div>
            `;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('results').innerHTML = '';
        }

        function runErrorHandlingTests() {
            document.getElementById('status').textContent = 'Running tests...';
            clearLog();
            
            log('Starting WebSocket error handling tests...');

            // Test 1: Error with missing message property
            try {
                const errorWithoutMessage = { code: 'CONNECTION_FAILED', type: 'TransportError' };
                const result = getErrorMessage(errorWithoutMessage);
                
                if (result.includes('CONNECTION_FAILED') && result.includes('TransportError')) {
                    addResult('Error without message property', true, `Correctly handled: "${result}"`);
                    log('✅ Test 1 passed: Error without message property handled correctly');
                } else {
                    addResult('Error without message property', false, `Unexpected result: "${result}"`);
                    log('❌ Test 1 failed: Error without message property not handled correctly');
                }
            } catch (e) {
                addResult('Error without message property', false, `Exception thrown: ${e.message}`);
                log('❌ Test 1 failed with exception: ' + e.message);
            }

            // Test 2: Null/undefined error
            try {
                const nullResult = getErrorMessage(null);
                const undefinedResult = getErrorMessage(undefined);
                
                if (nullResult === 'Unknown error' && undefinedResult === 'Unknown error') {
                    addResult('Null/undefined error', true, 'Correctly handled null and undefined errors');
                    log('✅ Test 2 passed: Null/undefined errors handled correctly');
                } else {
                    addResult('Null/undefined error', false, `Unexpected results: null="${nullResult}", undefined="${undefinedResult}"`);
                    log('❌ Test 2 failed: Null/undefined errors not handled correctly');
                }
            } catch (e) {
                addResult('Null/undefined error', false, `Exception thrown: ${e.message}`);
                log('❌ Test 2 failed with exception: ' + e.message);
            }

            // Test 3: String error
            try {
                const stringError = 'Network timeout occurred';
                const result = getErrorMessage(stringError);
                
                if (result === stringError) {
                    addResult('String error', true, `Correctly handled: "${result}"`);
                    log('✅ Test 3 passed: String error handled correctly');
                } else {
                    addResult('String error', false, `Unexpected result: "${result}"`);
                    log('❌ Test 3 failed: String error not handled correctly');
                }
            } catch (e) {
                addResult('String error', false, `Exception thrown: ${e.message}`);
                log('❌ Test 3 failed with exception: ' + e.message);
            }

            // Test 4: Error with message property
            try {
                const errorWithMessage = new Error('Connection refused');
                const result = getErrorMessage(errorWithMessage);
                
                if (result === 'Connection refused') {
                    addResult('Error with message', true, `Correctly handled: "${result}"`);
                    log('✅ Test 4 passed: Error with message handled correctly');
                } else {
                    addResult('Error with message', false, `Unexpected result: "${result}"`);
                    log('❌ Test 4 failed: Error with message not handled correctly');
                }
            } catch (e) {
                addResult('Error with message', false, `Exception thrown: ${e.message}`);
                log('❌ Test 4 failed with exception: ' + e.message);
            }

            // Test 5: Complex object error
            try {
                const complexError = { 
                    type: 'TransportError', 
                    description: 'xhr poll error',
                    context: { transport: 'polling' }
                };
                const result = getErrorMessage(complexError);
                
                if (result.includes('TransportError') && result.includes('xhr poll error')) {
                    addResult('Complex object error', true, `Correctly serialized: "${result}"`);
                    log('✅ Test 5 passed: Complex object error handled correctly');
                } else {
                    addResult('Complex object error', false, `Unexpected result: "${result}"`);
                    log('❌ Test 5 failed: Complex object error not handled correctly');
                }
            } catch (e) {
                addResult('Complex object error', false, `Exception thrown: ${e.message}`);
                log('❌ Test 5 failed with exception: ' + e.message);
            }

            // Test 6: Non-serializable object
            try {
                const circularObj = {};
                circularObj.self = circularObj; // Create circular reference
                const result = getErrorMessage(circularObj);
                
                if (result === 'Error object could not be serialized') {
                    addResult('Non-serializable object', true, `Correctly handled: "${result}"`);
                    log('✅ Test 6 passed: Non-serializable object handled correctly');
                } else {
                    addResult('Non-serializable object', false, `Unexpected result: "${result}"`);
                    log('❌ Test 6 failed: Non-serializable object not handled correctly');
                }
            } catch (e) {
                addResult('Non-serializable object', false, `Exception thrown: ${e.message}`);
                log('❌ Test 6 failed with exception: ' + e.message);
            }

            document.getElementById('status').textContent = 'Tests completed!';
            log('All tests completed.');
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            log('Page loaded. Ready to run tests.');
        });
    </script>
</body>
</html>
