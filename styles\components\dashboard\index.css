/* Dashboard Components CSS - Main Import File */

/* Ensure full height background */
html, body {
  @apply min-h-screen w-full m-0 p-0;
  background-color: #f5f7fb;
}

#__next {
  @apply min-h-screen w-full;
}

/* Remove any default margins/padding that might cause gaps */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

/* Import all dashboard component styles */
@import './stats-card.css';
@import './header.css';
@import './chart-card.css';
@import './progress-card.css';
@import './ocean-card.css';
@import './viais-card.css';
@import './world-map-card.css';
@import './assessment-table.css';
@import './responsive.css';

/* Dashboard Global Variables */
:root {
  /* Color Palette */
  --dashboard-text-primary: #1e1e1e;
  --dashboard-text-secondary: #64707d;
  --dashboard-text-light: #f1f1f1;
  --dashboard-primary-blue: #6475e9;
  --dashboard-primary-blue-hover: #5a6bd8;
  --dashboard-light-blue: #a2acf2;
  --dashboard-border: #eaecf0;
  --dashboard-background-white: #ffffff;
  --dashboard-background-light: #f3f3f3;
  --dashboard-background-card: #f8f9fa;
  --dashboard-background-indicator: #e5e7eb;
  
  /* Status Colors */
  --dashboard-success-bg: #dcfce7;
  --dashboard-success-text: #166534;
  --dashboard-success-border: #bbf7d0;
  --dashboard-warning-bg: #fef3c7;
  --dashboard-warning-text: #92400e;
  --dashboard-warning-border: #fde68a;
  --dashboard-danger-text: #dc2626;
  --dashboard-danger-bg: #fef2f2;
  --dashboard-danger-hover: #b91c1c;
  
  /* Spacing */
  --dashboard-spacing-xs: 0.25rem;
  --dashboard-spacing-sm: 0.5rem;
  --dashboard-spacing-md: 1rem;
  --dashboard-spacing-lg: 1.5rem;
  --dashboard-spacing-xl: 2rem;
  
  /* Border Radius */
  --dashboard-radius-sm: 0.375rem;
  --dashboard-radius-md: 0.5rem;
  --dashboard-radius-lg: 0.75rem;
  --dashboard-radius-xl: 1rem;
  --dashboard-radius-2xl: 1.5rem;
  
  /* Shadows */
  --dashboard-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --dashboard-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  
  /* Typography */
  --dashboard-font-size-xs: 0.75rem;
  --dashboard-font-size-sm: 0.875rem;
  --dashboard-font-size-base: 1rem;
  --dashboard-font-size-lg: 1.125rem;
  --dashboard-font-size-xl: 1.25rem;
  --dashboard-font-size-2xl: 1.5rem;
  --dashboard-font-size-3xl: 1.875rem;
  
  /* Chart Dimensions */
  --dashboard-chart-height: 128px;
  --dashboard-chart-bar-min-height: 20px;
  --dashboard-chart-bar-width: 2rem;
  
  /* Icon Sizes */
  --dashboard-icon-sm: 1rem;
  --dashboard-icon-md: 1.5rem;
  --dashboard-icon-lg: 2rem;
  
  /* Avatar Sizes */
  --dashboard-avatar-sm: 2rem;
  --dashboard-avatar-md: 2.5rem;
  --dashboard-avatar-lg: 4rem;
}

/* Dashboard Layout Utilities */
.dashboard-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.dashboard-grid {
  @apply grid gap-6;
}

.dashboard-grid--2-cols {
  @apply grid-cols-1 lg:grid-cols-2;
}

.dashboard-grid--3-cols {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.dashboard-grid--4-cols {
  @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
}

/* Common Dashboard Component Patterns */
.dashboard-card {
  @apply bg-white rounded-lg border shadow-sm;
  border-color: var(--dashboard-border);
}

.dashboard-card--elevated {
  @apply shadow-md;
}

.dashboard-text--primary {
  color: var(--dashboard-text-primary);
}

.dashboard-text--secondary {
  color: var(--dashboard-text-secondary);
}

.dashboard-text--light {
  color: var(--dashboard-text-light);
}

.dashboard-bg--primary {
  background-color: var(--dashboard-primary-blue);
}

.dashboard-bg--primary:hover {
  background-color: var(--dashboard-primary-blue-hover);
}

/* Responsive Design Helpers */
/* Mobile First Approach - Preserve desktop sizes, optimize for mobile */

/* Mobile devices (up to 640px) */
@media (max-width: 640px) {
  .dashboard-grid {
    @apply gap-3;
  }

  .dashboard-container {
    @apply px-3;
  }

  /* Mobile-specific dashboard layout */
  .dashboard-mobile-stack {
    @apply flex flex-col space-y-4;
  }

  /* Ensure cards maintain minimum readable size on mobile */
  .dashboard-card {
    @apply min-h-[120px];
  }

  /* Mobile stats cards - stack in 1 column for very small screens */
  .dashboard-stats-mobile {
    @apply grid grid-cols-1 gap-3;
  }

  /* Mobile stats cards - 2 columns for slightly larger mobile screens */
  .dashboard-stats-mobile-2col {
    @apply grid grid-cols-2 gap-3;
  }
}

/* Tablet devices (641px to 768px) */
@media (min-width: 641px) and (max-width: 768px) {
  .dashboard-grid {
    @apply gap-4;
  }

  .dashboard-container {
    @apply px-4;
  }

  /* Tablet layout - single column with proper spacing */
  .dashboard-tablet-layout {
    @apply grid grid-cols-1 gap-6;
  }

  /* Stats cards for tablet - maintain 2x2 or 4x1 layout */
  .dashboard-stats-tablet {
    @apply grid grid-cols-2 sm:grid-cols-4 gap-4;
  }
}

/* Large tablets and small desktops (769px to 1023px) */
@media (min-width: 769px) and (max-width: 1023px) {
  .dashboard-container {
    @apply px-6;
  }

  /* Maintain desktop-like layout but with adjusted spacing */
  .dashboard-medium-layout {
    @apply grid grid-cols-1 lg:grid-cols-3 gap-5;
  }
}

/* Desktop and larger (1024px+) - Preserve original sizes */
@media (min-width: 1024px) {
  .dashboard-container {
    @apply px-8;
  }

  /* Original desktop layout preserved */
  .dashboard-desktop-layout {
    @apply grid grid-cols-1 lg:grid-cols-3 gap-6;
  }

  /* Desktop stats cards maintain original 4-column layout */
  .dashboard-stats-desktop {
    @apply grid grid-cols-2 md:grid-cols-4 gap-4;
  }
}

/* Animation Utilities */
.dashboard-transition {
  @apply transition-all duration-300 ease-in-out;
}

.dashboard-fade-in {
  @apply animate-in fade-in duration-500;
}

/* Focus States */
.dashboard-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  --tw-ring-color: var(--dashboard-primary-blue);
}

/* Responsive Layout Classes */
.dashboard-responsive-container {
  @apply w-full max-w-[88rem] mx-auto;
  padding: 0.5rem;
  min-height: auto;
  display: flex;
  flex-direction: column;
}

@media (min-width: 480px) {
  .dashboard-responsive-container {
    padding: 0.75rem;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .dashboard-responsive-container {
    padding: 1rem;
  }
}

@media (min-width: 1024px) {
  .dashboard-responsive-container {
    padding: 1.5rem;
  }
}

/* Ensure content visibility */
.dashboard-responsive-container > * {
  width: 100%;
  flex-shrink: 0;
}

/* Responsive Grid Layouts */
.dashboard-main-grid {
  @apply grid gap-3;
  grid-template-columns: 1fr;
}

@media (min-width: 641px) and (max-width: 1023px) {
  .dashboard-main-grid {
    @apply gap-4;
    grid-template-columns: 1fr;
  }
}

@media (min-width: 1024px) {
  .dashboard-main-grid {
    @apply gap-6;
    grid-template-columns: 2fr 1fr;
  }
}

/* Stats Cards Responsive Grid */
.dashboard-stats-grid {
  @apply grid gap-2;
  grid-template-columns: 1fr;
}

@media (min-width: 480px) {
  .dashboard-stats-grid {
    @apply gap-3;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .dashboard-stats-grid {
    @apply gap-3;
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-stats-grid {
    @apply gap-4;
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Sidebar Responsive Layout */
.dashboard-sidebar {
  @apply space-y-3;
}

@media (min-width: 641px) and (max-width: 1023px) {
  .dashboard-sidebar {
    @apply space-y-3;
  }
}

@media (min-width: 1024px) {
  .dashboard-sidebar {
    @apply space-y-4;
  }
}

/* Card Responsive Adjustments */
.dashboard-card-responsive {
  @apply bg-white rounded-lg border shadow-sm;
  border-color: var(--dashboard-border);
  min-height: 120px;
}

@media (min-width: 768px) {
  .dashboard-card-responsive {
    min-height: auto;
  }
}
