/* Dashboard Header Component Styles */
.dashboard-header {
  @apply flex items-start justify-between w-full;
  margin-bottom: 1rem;
}

.dashboard-header__left {
  @apply flex items-center gap-4;
}

.dashboard-header__logo-container {
  @apply w-16 h-16 rounded-full flex items-center justify-center;
  background-color: #6475e9;
}

.dashboard-header__logo {
  @apply w-8 h-8 object-contain;
}

.dashboard-header__text-container {
  /* Container for title and description */
}

.dashboard-header__title {
  @apply text-2xl font-semibold;
  color: #1e1e1e;
  line-height: 1.2;
}

.dashboard-header__description {
  @apply text-sm mt-1;
  color: #64707d;
  line-height: 1.4;
}

.dashboard-header__right {
  @apply flex items-center gap-4;
}

.dashboard-header__user-button {
  @apply relative h-10 w-10 rounded-full;
}

.dashboard-header__avatar {
  @apply h-10 w-10;
}

.dashboard-header__avatar-fallback {
  @apply text-white;
  background-color: #6475e9;
}

.dashboard-header__dropdown {
  @apply w-56;
}

.dashboard-header__user-info {
  @apply flex flex-col space-y-1;
}

.dashboard-header__username {
  @apply text-sm font-medium leading-none;
}

.dashboard-header__email {
  @apply text-xs leading-none text-muted-foreground;
}

.dashboard-header__menu-item {
  /* Base menu item styles */
}

.dashboard-header__menu-item--danger {
  @apply text-red-600;
}

.dashboard-header__menu-icon {
  @apply mr-2 h-4 w-4;
}

/* Responsive adjustments for header */
@media (max-width: 640px) {
  .dashboard-header {
    @apply flex-col items-start gap-3 w-full;
  }

  .dashboard-header__left {
    @apply gap-3 w-full;
  }

  .dashboard-header__logo-container {
    @apply w-12 h-12;
  }

  .dashboard-header__logo {
    @apply w-6 h-6;
  }

  .dashboard-header__text-container {
    @apply flex-1;
  }

  .dashboard-header__title {
    @apply text-xl font-semibold;
    color: #1e1e1e;
    margin: 0;
  }

  .dashboard-header__description {
    @apply text-sm mt-1;
    color: #64707d;
    margin: 0;
  }

  .dashboard-header__right {
    @apply w-full justify-end;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .dashboard-header {
    @apply flex-row items-start justify-between w-full;
  }

  .dashboard-header__title {
    @apply text-xl font-semibold;
    color: #1e1e1e;
  }

  .dashboard-header__description {
    @apply text-sm mt-1;
    color: #64707d;
  }

  .dashboard-header__logo-container {
    @apply w-14 h-14;
  }

  .dashboard-header__logo {
    @apply w-7 h-7;
  }
}

/* Preserve desktop layout */
@media (min-width: 1024px) {
  .dashboard-header {
    @apply flex-row items-start justify-between w-full;
  }

  .dashboard-header__title {
    @apply text-2xl font-semibold;
    color: #1e1e1e;
  }

  .dashboard-header__description {
    @apply text-sm mt-1;
    color: #64707d;
  }

  .dashboard-header__logo-container {
    @apply w-16 h-16;
  }

  .dashboard-header__logo {
    @apply w-8 h-8;
  }
}
